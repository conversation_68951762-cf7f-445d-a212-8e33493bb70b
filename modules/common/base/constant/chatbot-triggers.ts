import { AppLanguage } from "../types/typing";

/**
 * Trigger phrases that customers can send to disable the chatbot and request human assistance
 */
export const CHATBOT_DISABLE_TRIGGERS = {
    ENGLISH: [
        "admin will respond",
        "I need support",
    ],
    PERSIAN: [
        "ادمین پاسخ دهد",
        "نیاز به پشتیبانی دارم",
    ]
};

/**
 * Trigger phrases that admin/owner can send to disable the chatbot for specific clients
 */
export const ADMIN_DISABLE_TRIGGERS = {
    ENGLISH: [
        "support will respond",
        "admin will respond",
    ],
    PERSIAN: [
        " توسط پشتیبان پاسخ داده میشود",
        "ادمین پاسخ میدهد",
    ]
};

/**
 * Confirmation messages sent when chatbot is disabled due to trigger phrases
 */
export const CHATBOT_DISABLE_CONFIRMATIONS = {
    ENGLISH: "Your request for admin assistance has been received. You will get a response soon.",
    PERSIAN: "درخواست شما برای صحبت با ادمین ثبت شد. به زودی پاسخ خواهید گرفت."
};

/**
 * System notification messages saved to chat history when admin assistance is requested
 */
export const ADMIN_ASSISTANCE_NOTIFICATIONS = {
    ENGLISH: "🔔 Customer requested admin assistance - Chatbot disabled",
    PERSIAN: "🔔 مشتری درخواست کمک ادمین کرده - ربات چت غیرفعال شد"
};

/**
 * System notification messages saved to chat history when owner disables chatbot
 */
export const OWNER_DISABLE_NOTIFICATIONS = {
    ENGLISH: "🔧 Support disabled chatbot for this conversation",
    PERSIAN: "🔧 پشتیبان چت را برای این مکالمه غیرفعال کرد"
};

/**
 * Get all trigger phrases for the current language (for customers)
 */
export function getTriggerPhrases(language: AppLanguage): string[] {
    if (language === "en") {
        return [...CHATBOT_DISABLE_TRIGGERS.ENGLISH, ...CHATBOT_DISABLE_TRIGGERS.PERSIAN];
    }
    return [...CHATBOT_DISABLE_TRIGGERS.PERSIAN, ...CHATBOT_DISABLE_TRIGGERS.ENGLISH];
}

/**
 * Get admin trigger phrases for the current language (for admin/owner)
 */
export function getAdminTriggerPhrases(language: AppLanguage): string[] {
    if (language === "en") {
        return [...ADMIN_DISABLE_TRIGGERS.ENGLISH, ...ADMIN_DISABLE_TRIGGERS.PERSIAN];
    }
    return [...ADMIN_DISABLE_TRIGGERS.PERSIAN, ...ADMIN_DISABLE_TRIGGERS.ENGLISH];
}

/**
 * Get confirmation message for the current language
 */
export function getConfirmationMessage(language: AppLanguage): string {
    return language === "en"
        ? CHATBOT_DISABLE_CONFIRMATIONS.ENGLISH
        : CHATBOT_DISABLE_CONFIRMATIONS.PERSIAN;
}

/**
 * Get admin assistance notification message for the current language
 */
export function getAdminAssistanceNotification(language: AppLanguage): string {
    return language === "en"
        ? ADMIN_ASSISTANCE_NOTIFICATIONS.ENGLISH
        : ADMIN_ASSISTANCE_NOTIFICATIONS.PERSIAN;
}

/**
 * Get owner disable notification message for the current language
 */
export function getOwnerDisableNotification(language: AppLanguage): string {
    return language === "en"
        ? OWNER_DISABLE_NOTIFICATIONS.ENGLISH
        : OWNER_DISABLE_NOTIFICATIONS.PERSIAN;
}
