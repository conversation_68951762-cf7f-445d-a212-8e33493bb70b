import { EntitySchema } from "typeorm";
import { User } from "./types";
import {
    AppLanguage,
    REGISTRATION_METHOD,
} from "../../../common/base/types/typing";

export const UserSchema = new EntitySchema<User>({
    name: "user",
    columns: {
        id: {
            type: Number,
            primary: true,
            generated: true,
        },
        state: {
            type: String,
            enum: ["OTP_NOT_VERIFIED", "REGISTERED"],
            default: "OTP_NOT_VERIFIED",
        },
        phone: {
            type: String,
            nullable: true,
        },
        email: {
            type: String,
            nullable: true,
        },
        registrationMethod: {
            type: String,
            enum: Object.values(REGISTRATION_METHOD),
            default: REGISTRATION_METHOD.PHONE,
        },
        role: {
            type: String,
            default: "user",
        },
        createdAt: {
            type: Date,
            createDate: true,
        },
        updatedAt: {
            type: Date,
            updateDate: true,
        },
        language: {
            type: String, //ISO-639-1
            enum: ["en", "fa"],
            default: "fa",
        },
    },
    relations: {},
});
