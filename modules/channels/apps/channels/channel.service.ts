import { delay, inject, injectable, registry } from "tsyringe";
import <PERSON><PERSON><PERSON><PERSON> from "./channel.repo";
import {
    EditChannelDto,
    InstagramMessageEntryDto,
    InstagramMessageEventsDto,
    InstagramSubscribeToEventsDto,
} from "./types";
import { getChannelSerializer as getChannelSerializer } from "./responses";
import { errors, instagram, utils } from "../../../common";
import { Job, Processor } from "bullmq";
import { RedisConnection } from "../../../common/lib/redis";
import { BullMQModule, createJobProcessor } from "../../../common/lib/bullmq";
import { registries } from "./registries";
import {
    getAdminAssistanceNotification,
    getAdminTriggerPhrases,
    getConfirmationMessage,
    getOwnerDisableNotification,
    getTriggerPhrases,
} from "../../../common/base/constant/chatbot-triggers";
import ChatsService from "../../../chat/apps/chat/chat.service";
import ClientService from "../../../users/apps/client/client.service";
import AutomationsService from "../../../automation/apps/automation/automation.service";
import InventoryService from "../../../inventories/apps/inventories/inventories.service";
import OrdersService from "../../../orders/apps/orders/order.service";
import Logger from "../../../common/lib/metrics/logger";
import { getSuccessMessage } from "../../../common/lib/errors/locales/success-messages";
import {
    channelNotFoundErrorLog,
    createChannel,
    recordErrorValue,
    updateChannelByIdLog,
} from "../../../common/lib/metrics/metrics";
import { EmbeddingService } from "../../../common/lib/chatbot/embedding";
import { RAG } from "../../../common/lib/chatbot/rag";
import {
    AppLanguage,
    CART_ACTION,
    CHATBOT_MESSAGE_TYPES,
    CardHelperFunctions,
    INSTAGRAM_RESPONSE_TYPE,
    InstagramResponse,
    JOB_EVENTS,
} from "../../../common/base/types/typing";
import { InstagramChallengeDto } from "../../../common/base/types/instagram.type";
import { th } from "@faker-js/faker";
import NotificationService from "../../../notifications/apps/notifications/notification.service";
import SubscriptionLimitService from "../../../subscriptions/apps/subscription-limit/subscription-limit.service";
import { UserLanguageService } from "../../../common/lib/services/user-language.service";
const redisConnection = new RedisConnection();

@registry(registries)
@injectable()
export default class ChannelService {
    private _redisConnection: RedisConnection;
    private _subscribeToEventsQueue: BullMQModule<InstagramSubscribeToEventsDto>;

    private _subscribeToEventsJobName: string = JOB_EVENTS.SUBSCRIBE_TO_EVENTS;

    constructor(
        private _repo: ChannelRepo,
        private _instagramService: instagram.AuthService,
        private _chatService: ChatsService,
        private _clientService: ClientService,
        private _automationsService: AutomationsService,
        private _notificationService: NotificationService,
        private _subscriptionLimitService: SubscriptionLimitService,
        private _userLanguageService: UserLanguageService,
        @inject(delay(() => InventoryService))
        private _inventoryService: InventoryService,


        @inject(delay(() => OrdersService))
        private _ordersService: OrdersService,
        private _embeddingService: EmbeddingService,
        @inject("queue")
        createQueueModule: <T>(
            queueName: string,
            processor: Processor<T>,
            redisConnection: RedisConnection,
        ) => BullMQModule<T>,
        @inject("redis")
        createRedisConnection: () => RedisConnection,
    ) {
        this._redisConnection = createRedisConnection();

        const processor: Processor<InstagramSubscribeToEventsDto> =
            createJobProcessor(this._subscribeToEvents.bind(this));
        this._subscribeToEventsQueue = createQueueModule(
            this._subscribeToEventsJobName,
            processor,
            this._redisConnection,
        );
    }

    get subscribeToEventsQueue() {
        return this._subscribeToEventsQueue;
    }

    // Helper method to get user's language from database
    // Used in webhook contexts where no HTTP request is available
    private async _getUserLanguage(userId: number): Promise<AppLanguage> {
        return await this._userLanguageService.getUserLanguage(userId);
    }

    private _subscribeToEvents = async (
        job: Job<InstagramSubscribeToEventsDto>,
    ) => {
        const { channelId, accessToken, accessTokenExpiresAt } = job.data;

        const now = new Date();
        const expireDateLimit = new Date();
        expireDateLimit.setDate(now.getDate() + 10);

        if (
            utils.isNil(accessTokenExpiresAt) ||
            new Date(accessTokenExpiresAt) < expireDateLimit
        ) {
            const longLivedTokenData =
                await this._instagramService.refreshLongLivedToken(accessToken);
            const data = {
                accessToken: longLivedTokenData.access_token,
                accessTokenExpiresAt: new Date(
                    now.getTime() + longLivedTokenData.expires_in * 1000,
                ),
            };
            await this._repo.updateById(channelId, data);
        }
        const instaAccountService = new instagram.AccountService(
            accessToken,
            false,
        ); // Enable test mode
        const data = await instaAccountService.subscribeToEvents();
        if (!data.success) {
            throw new Error(JSON.stringify(data));
        }
    };

    private _generateInstagramResponse = async (
        text: string,
        userId: number,
        helpers: CardHelperFunctions,
        clientId?: number,
    ): Promise<InstagramResponse | undefined> => {
        // Get user's language from database (no context needed in webhook environment)
        const userLanguage = await this._getUserLanguage(userId);
        
        // Automation Response
        const automationResponses =
            await this._automationsService.processAutomation({
                text,
                userId,
            });

        if (automationResponses.length > 0) {
            return {
                type: INSTAGRAM_RESPONSE_TYPE.AUTOMATION,
                messages: automationResponses
                    .filter(utils.isNotNil)
                    .map((response) => {
                        // Handle both old string format and new object format
                        if (typeof response === 'string') {
                            return { text: response };
                        } else if (response.type === 'text') {
                            return { text: response.content };
                        } else if (response.type === 'file') {
                            return { text: "", payload: { file: response.content } };
                        }
                        return { text: response.content || "" };
                    }),
            };
        }

        // Chatbot Response
        const rag = new RAG(
            redisConnection,
            {
                userId,
                clientId: clientId ?? utils.generateUuId(),
                helpers,
                userLanguage, // Now using dynamic user language with context priority
            },
            this._ordersService,
            this._inventoryService
        );

        const botResponse = await rag.processMessage({
            text,
            type: CHATBOT_MESSAGE_TYPES.USER,
        });

        return {
            messages: [
                {
                    text: botResponse,
                },
            ],
            type: INSTAGRAM_RESPONSE_TYPE.CHATBOT,
        };
    };

    private _processInstagramMessageEntry = async (
        entry: InstagramMessageEntryDto,
        testMode = false,
    ) => {
        const channelPlatformId = entry.id;
        const channel = await this._repo.findOneByQuery({
            platformId: channelPlatformId,
        });
        if (utils.isNil(channel)) {
            return;
        }
        const responses: string[] = []

        // Get user language context early in the process
        // TODO: This could be enhanced to get language from channel.userLanguage if that field exists
        const userLanguageContext = await this._getUserLanguage(channel.userId);

        const accountService = new instagram.AccountService(
            channel.accessToken,
            testMode,
        ); // Enable test mode

        for (const messaging of entry.messaging) {
            const isFromChannel = messaging.sender.id === channelPlatformId;

            const clientPlatformId = isFromChannel
                ? messaging.recipient.id
                : messaging.sender.id;

            // Check if this is a manual response from the owner to a client
            // Consider that sender id from owner and bot are the same!
            if (isFromChannel) {
                // Handle owner messages - check if owner wants to disable chatbot
                const { mid, text } = messaging.message;
                const sanitizedText = utils.arabicToPersian(text);

                if (!sanitizedText) {
                    continue;
                }

                // Check if owner sent an admin trigger phrase to disable chatbot
                const adminTriggerPhrases =
                    getAdminTriggerPhrases(userLanguageContext);
                const messageText = sanitizedText.toLowerCase().trim();
                const shouldDisableChatbot = adminTriggerPhrases.some(
                    (phrase) => messageText.includes(phrase.toLowerCase()),
                );

                if (shouldDisableChatbot) {
                    // Find the client that the owner is responding to
                    const targetClient =
                        await this._clientService.getClientOfChannelByPlatformId(
                            channel.id,
                            clientPlatformId,
                        );

                    if (targetClient) {
                        console.log(
                            "Owner requested to disable chatbot for client",
                            {
                                ownerId: channel.userId,
                                clientId: targetClient.id,
                                clientPlatformId,
                                triggerMessage: sanitizedText,
                            },
                        );

                        // Save the owner's message and disable chatbot for the target client
                        await this._repo.runTransaction(async (manager) => {
                            // Save the owner's trigger message to chat history
                            await this._chatService.addChatHistory(
                                {
                                    channelId: channel.id,
                                    clientId: targetClient.id,
                                    userId: channel.userId,
                                    uniqueKey: mid,
                                    text: sanitizedText,
                                    type: "platform",
                                },
                                manager,
                            );

                            // Save owner disable notification to chat history
                            await this._chatService.addChatHistory(
                                {
                                    channelId: channel.id,
                                    clientId: targetClient.id,
                                    userId: channel.userId,
                                    uniqueKey: utils.generateUuId(),
                                    text: getOwnerDisableNotification(
                                        userLanguageContext,
                                    ),
                                    type: "system",
                                },
                                manager,
                            );
                        });

                        // Disable chatbot for this specific client
                        await this._clientService.editClient(
                            targetClient.id,
                            { messagingEnabled: false },
                            { id: channel.userId } as Express.User,
                        );

                        // Send notification message to customer
                        const ownerDisableMessage =
                            getOwnerDisableNotification(userLanguageContext);
                        await accountService.sendMessage(
                            clientPlatformId,
                            ownerDisableMessage,
                        );

                        console.log(
                            "Chatbot disabled for client by owner:",
                            targetClient.id,
                        );
                    } else {
                        console.log(
                            "Owner sent trigger phrase but no target client found",
                            {
                                ownerId: channel.userId,
                                clientPlatformId,
                                triggerMessage: sanitizedText,
                            },
                        );
                    }
                }
                continue;
            }

            const { mid, text } = messaging.message;

            const sanitizedText = (text && typeof text == "string") && utils.arabicToPersian(text);

            if (!sanitizedText) {
                continue;
            }

            let client =
                await this._clientService.getClientOfChannelByPlatformId(
                    channel.id,
                    clientPlatformId,
                );

            // Check if messaging is enabled for this specific client
            if (client && !client.messagingEnabled) {
                console.log(
                    "Messaging is disabled for this client, skipping chatbot response",
                    {
                        clientId: client.id,
                        clientPlatformId,
                    },
                );
                continue;
            }

            // Check for trigger phrases to disable chatbot and request human assistance
            const triggerPhrases = getTriggerPhrases(userLanguageContext);

            const messageText = sanitizedText.toLowerCase().trim();
            const shouldDisableChatbot = triggerPhrases.some((phrase) =>
                messageText.includes(phrase.toLowerCase()),
            );

            if (shouldDisableChatbot) {
                console.log(
                    "Customer requested human assistance, disabling chatbot",
                    {
                        clientId: client?.id,
                        clientPlatformId,
                        triggerMessage: sanitizedText,
                    },
                );

                // Save the trigger message to chat history and disable chatbot
                await this._repo.runTransaction(async (manager) => {
                    // Create client if doesn't exist
                    if (utils.isNil(client)) {
                        const clientProfile =
                            await accountService.getUserProfile(
                                clientPlatformId,
                            );
                        client = await this._clientService.addClient(
                            {
                                channelId: channel.id,
                                platformId: clientPlatformId,
                                userId: channel.userId,
                                username: clientProfile.username,
                            },
                            manager,
                        );
                    }

                    // Save the trigger message to chat history
                    await this._chatService.addChatHistory(
                        {
                            channelId: channel.id,
                            clientId: client!.id,
                            userId: channel.userId,
                            uniqueKey: mid,
                            text: sanitizedText,
                            type: "user",
                        },
                        manager,
                    );

                    // Save admin request notification to chat history
                    await this._chatService.addChatHistory(
                        {
                            channelId: channel.id,
                            clientId: client!.id,
                            userId: channel.userId,
                            uniqueKey: utils.generateUuId(),
                            text: getAdminAssistanceNotification(
                                userLanguageContext,
                            ),
                            type: "system",
                        },
                        manager,
                    );
                });

                // Disable chatbot for this client
                if (client) {
                    await this._clientService.editClient(
                        client.id,
                        { messagingEnabled: false },
                        { id: channel.userId } as Express.User,
                    );

                    this._notificationService.addDirectChatNotification(
                        {
                            id: client.id,
                            name: client.username,
                        }
                    );
                    console.log(
                        "Chatbot disabled for client due to trigger phrase:",
                        client.id,
                    );
                }

                // Send confirmation message to customer
                const confirmationMessage =
                    getConfirmationMessage(userLanguageContext);
                await accountService.sendMessage(
                    clientPlatformId,
                    confirmationMessage,
                );
                continue;
            }

            const sendInventoryImage = async (id: string) => {
                try {
                    console.log("send Inventory Image", { id });

                    // Validate the ID is a valid number
                    const inventoryId = parseInt(id);
                    if (isNaN(inventoryId)) {
                        Logger.warn("Invalid inventory ID format", {
                            action: "sendInventoryImage",
                            inventoryId: id,
                            clientPlatformId,
                            userId: channel.userId,
                        });
                        return false;
                    }

                    // Check if ID is within a reasonable range (assuming IDs are not extremely large)
                    if (inventoryId <= 0 || inventoryId > 999) {
                        Logger.warn("Inventory ID out of reasonable range", {
                            action: "sendInventoryImage",
                            inventoryId: id,
                            clientPlatformId,
                            userId: channel.userId,
                        });
                        return false;
                    }

                    // Get the inventory details first to ensure we're sending the correct product
                    let inventoryItem;
                    try {
                        // Get the full inventory item to include the name in logs
                        const userObj = { id: channel.userId } as Express.User;
                        inventoryItem =
                            await this._inventoryService.getInventoryWithAttributes(
                                userObj,
                                inventoryId,
                            );

                        Logger.info("Found inventory item for image", {
                            action: "sendInventoryImage",
                            inventoryId,
                            inventoryName: inventoryItem.name,
                            inventoryUserId: inventoryItem.userId,
                            requestingUserId: channel.userId,
                            clientPlatformId,
                            userId: channel.userId,
                            inventoryImageArray: inventoryItem.image,
                        });
                    } catch (err) {
                        Logger.warn("Inventory not found for this user", {
                            action: "sendInventoryImage",
                            inventoryId,
                            clientPlatformId,
                            userId: channel.userId,
                            error:
                                err instanceof Error
                                    ? err.message
                                    : String(err),
                        });
                        // Continue anyway to try getting the image - it might be a shared inventory
                    }

                    // Get the image
                    Logger.info("Requesting image from inventory service", {
                        action: "sendInventoryImage",
                        inventoryId,
                        clientPlatformId,
                        userId: channel.userId,
                    });

                    const image =
                        await this._inventoryService.getInventoryImage(
                            inventoryId,
                            channel.userId, // Pass the user ID to ensure ownership check
                        );

                    Logger.info("Received image from inventory service", {
                        action: "sendInventoryImage",
                        inventoryId,
                        clientPlatformId,
                        userId: channel.userId,
                        imageUrl: image,
                        imageExists: !utils.isNil(image),
                    });

                    if (utils.isNil(image)) {
                        Logger.warn("Image not found for inventory", {
                            action: "sendInventoryImage",
                            inventoryId,
                            clientPlatformId,
                            userId: channel.userId,
                        });
                        return false;
                    }

                    let fullImageUrl: string;
                    if (!image.startsWith("http")) {
                        fullImageUrl = process.env.STATIC_SERVER_URL + image;
                        await accountService.sendImage(
                            clientPlatformId,
                            fullImageUrl,
                        );
                    } else {
                        fullImageUrl = image;
                        await accountService.sendImage(
                            clientPlatformId,
                            fullImageUrl,
                        );
                    }

                    // Log the image URL and product name that was sent
                    Logger.info("Image sent via chatbot", {
                        action: "sendInventoryImage",
                        imageUrl: fullImageUrl,
                        inventoryId,
                        inventoryName: inventoryItem?.name || "Unknown",
                        clientPlatformId,
                        userId: channel.userId,
                    });

                    // We're no longer sending a follow-up message to avoid redundancy
                    // Just log that we've sent the image
                    if (inventoryItem?.name) {
                        Logger.info(
                            "Product image sent without confirmation message",
                            {
                                action: "sendInventoryImage",
                                inventoryId,
                                inventoryName: inventoryItem.name,
                                clientPlatformId,
                                userId: channel.userId,
                            },
                        );
                    }

                    return true;
                } catch (error) {
                    console.error(error);
                    Logger.error("Error sending inventory image", {
                        action: "sendInventoryImage",
                        inventoryId: id,
                        clientPlatformId,
                        userId: channel.userId,
                        error:
                            error instanceof Error
                                ? error.message
                                : String(error),
                    });
                    return false;
                }
            };

            const manageCart = async (
                id: string,
                action: CART_ACTION,
                quantity: number,
            ) => {
                try {
                    console.log("manageCart", { id });

                    // Proceed with the regular cart operation
                    await this._ordersService.manageOrder(
                        client!.userId,
                        client!.id,
                        {
                            id: parseInt(id),
                            amount:
                                quantity *
                                (action === CART_ACTION.ADD ? 1 : -1),
                        },
                    );

                    const currentOrder =
                        await this._ordersService.getClientActiveOrder(
                            client!.id,
                        );

                    return JSON.stringify(currentOrder);
                } catch (error) {
                    console.error(error);
                    return "Could not manage cart";
                }
            };

            const getCartsInfo = async () => {
                const orders = await this._ordersService.getClientOrders(
                    client!.id,
                );

                return JSON.stringify(orders);
            };


            // Create a getProductDetails function to pass to the RAG class
            const getProductDetails = async (id: string) => {
                try {
                    // Parse the ID to a number
                    const inventoryId = parseInt(id);
                    if (isNaN(inventoryId)) {
                        Logger.warn(`Invalid inventory ID format`, {
                            action: "getProductDetails",
                            inventoryId: id,
                            userId: channel.userId,
                        });
                        return null;
                    }

                    // Check if ID is within a reasonable range
                    if (inventoryId <= 0 || inventoryId > 1000000) {
                        Logger.warn(`Inventory ID out of reasonable range`, {
                            action: "getProductDetails",
                            inventoryId,
                            userId: channel.userId,
                        });
                        return null;
                    }

                    // Get the inventory item by ID with attributes
                    const userObj = { id: channel.userId } as Express.User;

                    // Get the inventory with attributes using the new method
                    const inventoryWithAttributes =
                        await this._inventoryService.getInventoryWithAttributes(
                            userObj,
                            inventoryId,
                        );

                    // Get total stock (not considering reserved)
                    const total = inventoryWithAttributes.total || 0;

                    // Debug logging for stock validation
                    console.log(
                        `[getProductDetails] Debug info for product ${inventoryId}:`,
                    );
                    console.log(
                        `[getProductDetails] - Name: ${inventoryWithAttributes.name}`,
                    );
                    console.log(
                        `[getProductDetails] - Total: ${inventoryWithAttributes.total}`,
                    );
                    console.log(
                        `[getProductDetails] - Reserved: ${inventoryWithAttributes.reserved}`,
                    );
                    console.log(
                        `[getProductDetails] - Calculated total: ${total}`,
                    );

                    // Format the product details
                    const productDetails: {
                        id: number;
                        name: string;
                        price: string;
                        attributes: Array<{ key: string; value: string }>;
                        total: number;
                        availableStock: number;
                    } = {
                        id: inventoryId, // Include the ID in the response for reference
                        name: inventoryWithAttributes.name,
                        price: inventoryWithAttributes.price.toString(),
                        attributes: [],
                        total,
                        availableStock: total, // Use total as available stock
                    };

                    // For English, ensure the price is stored as an integer in Redis
                    // by removing the decimal point (e.g., "16.23" -> "1623")
                    // This ensures BigInt conversion works in the order service
                    if (
                        userLanguageContext === "en" &&
                        productDetails.price &&
                        productDetails.price.includes(".")
                    ) {
                        const displayPrice = productDetails.price;
                        productDetails.price = productDetails.price.replace(
                            ".",
                            "",
                        );
                        console.log(
                            `[getProductDetails] Converted price from ${displayPrice} to ${productDetails.price} for BigInt compatibility`,
                        );
                    }

                    // Map attributes if they exist
                    if (
                        inventoryWithAttributes.attributes &&
                        Array.isArray(inventoryWithAttributes.attributes) &&
                        inventoryWithAttributes.attributes.length > 0
                    ) {
                        productDetails.attributes =
                            inventoryWithAttributes.attributes.map(
                                (attr: any) => ({
                                    key: attr.key,
                                    value: attr.value,
                                }),
                            );
                    }

                    Logger.info(`Product details retrieved successfully`, {
                        action: "getProductDetails",
                        inventoryId,
                        userId: channel.userId,
                    });

                    return productDetails;
                } catch (error) {
                    Logger.error(`Error getting product details`, {
                        action: "getProductDetails",
                        inventoryId: id,
                        userId: channel.userId,
                        error:
                            error instanceof Error
                                ? error.message
                                : String(error),
                    });
                    return null;
                }
            };

            // Check subscription limits before generating response
            const subscriptionCheck = await this._subscriptionLimitService.checkBotMessageLimit(channel.userId);
            
            if (!subscriptionCheck.isAllowed) {
                console.log("Bot message limit reached for user", {
                    userId: channel.userId,
                    currentUsage: subscriptionCheck.currentUsage,
                    limit: subscriptionCheck.limit,
                    clientPlatformId,
                });
                
                // Get user's language for the limit message
                const userLanguage = await this._getUserLanguage(channel.userId);
                const limitMessage = userLanguage === 'en' 
                    ? "You have reached your monthly chatbot message limit. Please upgrade your subscription to continue using the bot."
                    : "شما به حد مجاز پیام‌های ماهانه چت‌بات رسیده‌اید. لطفاً اشتراک خود را ارتقا دهید.";
                
                await accountService.sendMessage(clientPlatformId, limitMessage);
                continue;
            }

            // Handle notifications for limit warnings
            if (subscriptionCheck.shouldNotifyNearLimit) {
                console.log("User approaching bot message limit - 80% threshold reached", {
                    userId: channel.userId,
                    currentUsage: subscriptionCheck.currentUsage,
                    limit: subscriptionCheck.limit,
                    remainingUsage: subscriptionCheck.remainingUsage,
                    percentage: Math.round((subscriptionCheck.currentUsage / subscriptionCheck.limit) * 100),
                    clientPlatformId,
                });
                
                // Send warning notification
                await this._notificationService.addBotMessageLimitWarningNotification(
                    channel.userId,
                    subscriptionCheck.currentUsage,
                    subscriptionCheck.limit
                );
            }

            if (subscriptionCheck.shouldNotifyLimitReached) {
                console.log("User reached bot message limit - limit exceeded", {
                    userId: channel.userId,
                    currentUsage: subscriptionCheck.currentUsage,
                    limit: subscriptionCheck.limit,
                    clientPlatformId,
                });
                
                // Send limit reached notification
                await this._notificationService.addBotMessageLimitReachedNotification(
                    channel.userId,
                    subscriptionCheck.limit
                );
            }

            const response = await this._generateInstagramResponse(
                sanitizedText,
                channel.userId,
                {
                    sendInventoryImage,
                    manageCart,
                    getCartsInfo,
                    getProductDetails,
                },
                client?.id,
            );

            if (response?.messages) {
                responses.push(...response.messages.map((m) => m.text));
            }

            console.log("Instagram response:", JSON.stringify(response));

            await this._repo.runTransaction(async (manager) => {
                if (utils.isNil(client)) {
                    const clientProfile =
                        await accountService.getUserProfile(clientPlatformId);

                    client = await this._clientService.addClient(
                        {
                            channelId: channel.id,
                            platformId: clientPlatformId,
                            userId: channel.userId,
                            username: clientProfile.username,
                        },
                        manager,
                    );
                }

                await this._chatService.addChatHistory(
                    {
                        channelId: channel.id,
                        clientId: client!.id,
                        userId: channel.userId,
                        uniqueKey: mid,
                        text: sanitizedText,
                        type:
                            response?.type === "automation"
                                ? "platform"
                                : "user",
                    },
                    manager,
                );

                if (utils.isNil(response)) {
                    return;
                }

                if (response.type === "inventory" || response.type === "cart") {
                    await this._chatService.addChatHistory(
                        {
                            channelId: channel.id,
                            clientId: client!.id,
                            userId: channel.userId,
                            uniqueKey: utils.generateUuId(),
                            text: response.args!,
                            type: "ai",
                        },
                        manager,
                    );
                }

                for (const message of response.messages) {
                    await this._chatService.addChatHistory(
                        {
                            channelId: channel.id,
                            clientId: client!.id,
                            userId: channel.userId,
                            uniqueKey: utils.generateUuId(),
                            text: message.text,
                            type: ["inventory", "cart", "automation"].includes(
                                response.type,
                            )
                                ? "generated"
                                : "ai",
                        },
                        manager,
                    );
                }

                for (const message of response.messages) {
                    if (response.type === "automation") {
                        // Check if this is a file message
                        if (utils.isNotNil(message.payload) && message.payload.file) {
                            const filePath = message.payload.file as string;
                            const fileType = this._getFileType(filePath);
                            
                            if (fileType !== 'unknown') {
                                try {
                                    // Construct full URL if needed
                                    let fullFileUrl: string;
                                    if (!filePath.startsWith("http")) {
                                        fullFileUrl = process.env.STATIC_SERVER_URL + filePath;
                                    } else {
                                        fullFileUrl = filePath;
                                    }
                                    
                                    // Send appropriate file type
                                    switch (fileType) {
                                        case 'image':
                                            await accountService.sendImage(clientPlatformId, fullFileUrl);
                                            Logger.info("Image sent via automation", {
                                                action: "sendAutomationImage",
                                                fileUrl: fullFileUrl,
                                                clientPlatformId,
                                                userId: channel.userId,
                                            });
                                            break;
                                        case 'video':
                                            await accountService.sendVideo(clientPlatformId, fullFileUrl);
                                            Logger.info("Video sent via automation", {
                                                action: "sendAutomationVideo",
                                                fileUrl: fullFileUrl,
                                                clientPlatformId,
                                                userId: channel.userId,
                                            });
                                            break;
                                        case 'audio':
                                            await accountService.sendAudio(clientPlatformId, fullFileUrl);
                                            Logger.info("Audio sent via automation", {
                                                action: "sendAutomationAudio",
                                                fileUrl: fullFileUrl,
                                                clientPlatformId,
                                                userId: channel.userId,
                                            });
                                            break;
                                    }
                                } catch (error) {
                                    Logger.error("Failed to send automation file", {
                                        action: "sendAutomationFile",
                                        error: error instanceof Error ? error.message : String(error),
                                        filePath,
                                        fileType,
                                        clientPlatformId,
                                        userId: channel.userId,
                                    });
                                    
                                    // Fallback: send the file path as text if file sending fails
                                    if (message.text && message.text.trim()) {
                                        await accountService.sendMessage(
                                            clientPlatformId,
                                            message.text,
                                        );
                                    } else {
                                        await accountService.sendMessage(
                                            clientPlatformId,
                                            `File: ${filePath}`,
                                        );
                                    }
                                }
                            } else {
                                // For unsupported file types, send as text
                                const fileMessage = message.text && message.text.trim() 
                                    ? message.text 
                                    : `File: ${filePath}`;
                                    
                                await accountService.sendMessage(
                                    clientPlatformId,
                                    fileMessage,
                                );
                                
                                Logger.info("Unsupported file type sent as text via automation", {
                                    action: "sendAutomationFile",
                                    filePath,
                                    clientPlatformId,
                                    userId: channel.userId,
                                });
                            }
                        }
                        
                        // Send text message if there is any text content
                        if (message.text && message.text.trim()) {
                            await accountService.sendMessage(
                                clientPlatformId,
                                message.text,
                            );
                        }
                    } else if (response.type === "chatbot") {
                        await accountService.sendMessage(
                            clientPlatformId,
                            message.text,
                        );
                    } else if (response.type === "inventory") {
                        if (utils.isNotNil(message.payload)) {
                            await accountService.sendInventory(
                                clientPlatformId,
                                message.payload as {
                                    name: string;
                                    image: string;
                                },
                            );
                        }
                        await accountService.sendMessage(
                            clientPlatformId,
                            message.text,
                        );
                    } else if (response.type === "cart") {
                        await accountService.sendMessage(
                            clientPlatformId,
                            message.text,
                        );
                    }
                }
            });
        }
        return responses;
    };

    initInstagramSso = (profile: Express.User) => {
        const url = this._instagramService.createLoginUrl(String(profile.id));
        return { url };
    };

    processInstagramSso = async (code: string, userId: number | null) => {
        if (utils.isNil(code)) {
            Logger.error("code is not part of the url", {
                userId,
                route: "/api/v1/channels/instagram/callback",
                action: "processInstagramSso",
                error: "badRequestError",
            });

            recordErrorValue(
                "badRequestError",
                "code is not sent in processInstagramSso",
            );
            throw new errors.BadRequestError();
        }

        // For Instagram Basic Display API, we don't need to require userId in the state parameter
        // We'll create a new channel without associating it with a user initially
        // The user association can be done later if needed

        let currentChannel = null;
        if (!utils.isNil(userId)) {
            console.log("userId", userId);
            currentChannel = await this._repo.findOneByQuery({ userId });
            console.log("currentChannel", currentChannel);
            Logger.info("Found existing channel for user", {
                userId,
                action: "processInstagramSso",
            });
        } else {
            Logger.info(
                "No userId provided, will create a new channel without user association",
                {
                    action: "processInstagramSso",
                },
            );
        }

        const now = new Date();

        const accessTokenData =
            await this._instagramService.getAccessToken(code);

        // NOTE: The Instagram Basic Display API is being deprecated on December 4, 2024
        // We're using a workaround for the token exchange, but attempting to use the real API for other operations
        // The token exchange workaround uses short-lived tokens directly, which will expire in 1 hour
        // For a permanent solution before December 4, 2024, switch to a Business/Creator account and use the Instagram Graph API
        const longLivedTokenData =
            await this._instagramService.getLongLivedToken(
                accessTokenData.access_token,
            );

        const accountService = new instagram.AccountService(
            longLivedTokenData.access_token,
        );

        const profileData = await accountService.getProfile();

        console.log("profileData", profileData);

        // Restriction: Prevent connecting the same Instagram account to multiple active panels for the same user
        if (userId !== null) {
            // Find any other active channel for this user with the same Instagram ID (platformId)
            const existingPanel = await this._repo.findOneByQuery({
                userId,
                platformId: profileData.user_id,
                isDisconnected: false,
            });
            // If updating, allow the current channel; otherwise, block
            if (
                existingPanel &&
                (!currentChannel || existingPanel.id !== currentChannel.id)
            ) {
                Logger.error(
                    "Instagram account already connected to another active panel for this user",
                    {
                        userId,
                        platformId: profileData.user_id,
                        action: "processInstagramSso",
                        error: "Instagram account already connected",
                    },
                );
                throw new errors.BadRequestError();
            }
        }

        // Prepare data for channel creation/update
        const data: any = {
            accessToken: longLivedTokenData.access_token,
            accessTokenExpiresAt: new Date(
                now.getTime() + longLivedTokenData.expires_in * 1000,
            ),
            name: profileData.name,
            username: profileData.username,
            platformId: profileData.user_id,
            isDisconnected: false, // Ensure connected state when linking Instagram
        };

        // Only add userId if it's provided
        if (userId !== null) {
            data.userId = userId;
        }

        let channel = null;
        if (utils.isNotNil(currentChannel)) {
            await this._repo.updateById(currentChannel.id, data);
            updateChannelByIdLog.inc();
            Logger.info("update channel by id", {
                action: "process instagram sso > update channel by id",
            });
            channel = currentChannel;
        } else {
            // If we don't have a userId, we'll create a temporary channel
            // that can be associated with a user later
            createChannel.inc();
            Logger.info("create channel", {
                action: "process instagram sso > create channel",
                data: { ...data, accessToken: "[REDACTED]" },
            });
            channel = await this._repo.create(data);
        }

        await this._subscribeToEventsQueue.addJob(
            this._subscribeToEventsJobName,
            {
                channelId: channel.id,
                accessToken: longLivedTokenData.access_token,
                accessTokenExpiresAt: data.accessTokenExpiresAt,
            },
        );
    };

    verifyInstagramWebhook = (args: any) => {
        if (args?.["hub.mode"] !== "subscribe") {
            Logger.error("hub.mode is not subscribe", {
                action: "verify instagram webhook",
                error: "badRequestError",
            });
            recordErrorValue("badRequestError", "");
            throw new errors.BadRequestError();
        }
        const challenge = this._instagramService.verifyWebhook(
            args as InstagramChallengeDto,
        );

        return challenge;
    };

    processInstagramEvents = async (args: InstagramMessageEventsDto) => {
        for (const entry of args.entry) {
            await this._processInstagramMessageEntry(entry); //TODO:add to JOB
        }
    };

    getChannel = async (user: Express.User) => {
        const { id: userId } = user;

        const channel = await this._repo.getChannelForUser(userId);

        if (utils.isNil(channel)) {
            Logger.error("channel not found", {
                action: "getChannel",
                error: "not found error",
            });

            channelNotFoundErrorLog.inc();
            recordErrorValue("not found error", "Channel not found");

            throw new errors.NotFoundError("Channel");
        }

        return getChannelSerializer(channel);
    };

    getChannelOfUser = async (id: number, userId: number) => {
        const channel = await this._repo.findOneByQuery({
            id,
            userId,
        });

        if (utils.isNil(channel)) {
            Logger.error("channel not found", {
                action: "getChannelOfUser",
                error: "not found error",
            });

            channelNotFoundErrorLog.inc();
            recordErrorValue("not found error", "Channel not found");

            throw new errors.NotFoundError("Channel");
        }

        return channel;
    };

    getChannelOfUserByUserId = async (userId: number) => {
        const channel = await this._repo.findOneByQuery({
            userId,
        });

        if (utils.isNil(channel)) {
            channelNotFoundErrorLog.inc();
            Logger.error("Channel not found", {
                userId,
                statusCode: 404,
                error: "Channel not found",
            });

            recordErrorValue("not found error", "Channel not found");
            throw new errors.NotFoundError("Channel");
        }

        return channel;
    };

    updateChannel = async (args: EditChannelDto, profile: Express.User) => {
        const { id: userId } = profile;

        const channel = await this.getChannelOfUserByUserId(userId);

        updateChannelByIdLog.inc();
        Logger.info("successfully update channel", {
            action: "updateChannel",
        });

        await this._repo.updateOneByQuery({ userId, id: channel.id }, args);
    };

    disconnectInstagram = async (profile: Express.User) => {
        const { id: userId } = profile;

        try {
            // Get the user's channel
            const channel = await this.getChannelOfUserByUserId(userId);

            // Check if Instagram is actually connected
            if (
                utils.isNil(channel.accessToken) ||
                utils.isNil(channel.platformId) ||
                channel.isDisconnected
            ) {
                Logger.warn(
                    "Attempted to disconnect Instagram when not connected",
                    {
                        action: "disconnectInstagram",
                        userId,
                        channelId: channel.id,
                        hasAccessToken: !utils.isNil(channel.accessToken),
                        hasPlatformId: !utils.isNil(channel.platformId),
                        isDisconnected: channel.isDisconnected,
                    },
                );

                throw new errors.InstagramAlreadyDisconnectedError();
            }

            Logger.info("Starting Instagram disconnection", {
                action: "disconnectInstagram",
                userId,
                channelId: channel.id,
                username: channel.username,
                platformId: channel.platformId,
            });

            // Set disconnected flag instead of clearing data
            const disconnectData = {
                isDisconnected: true,
            };

            // Update channel with disconnect flag
            await this._repo.updateById(channel.id, disconnectData);

            Logger.info("Instagram account disconnected successfully", {
                action: "disconnectInstagram",
                userId,
                channelId: channel.id,
            });

            return {
                success: true,
                message: getSuccessMessage(
                    "INSTAGRAM_DISCONNECTED_SUCCESSFULLY",
                    profile.language as AppLanguage || "fa",
                ),
            };
        } catch (error) {
            Logger.error("Failed to disconnect Instagram account", {
                action: "disconnectInstagram",
                userId,
                error: error instanceof Error ? error.message : String(error),
            });

            // Re-throw known errors, wrap unknown errors
            if (
                error instanceof errors.BadRequestError ||
                error instanceof errors.NotFoundError ||
                error instanceof errors.InstagramAlreadyDisconnectedError
            ) {
                throw error;
            }

            throw new errors.InternalError();
        }
    };

    initSubscribingToEvents = async () => {
        const enabledChannels = await this._repo.getInstagramEnabledChannels();
        for (const channel of enabledChannels) {
            await this._subscribeToEventsQueue.addJob(
                this._subscribeToEventsJobName,
                {
                    channelId: channel.id,
                    accessToken: channel.accessToken,
                    accessTokenExpiresAt: channel.accessTokenExpiresAt,
                },
            );
        }
    };

    notifyOrderStatus = async (
        userId: number,
        clientId: number,
        status: string,
    ) => {
        // Get user's language preference
        const userLanguage = await this._getUserLanguage(userId);
        const isEnglish = userLanguage === "en";

        console.log(`[notifyOrderStatus] Current language: ${userLanguage}`);

        // Status mapping for Persian
        const statusToFa = (status: string) => {
            console.log({ status });
            const mapping = {
                PENDING_SELLER_REVIEW: "در انتظار بررسی فروشنده",
                CONFIRMED_WAITING_PAYMENT: "تایید شده و در انتظار پرداخت",
                PAYMENT_CONFIRMED_PROCESSING: "پرداخت شده و در حال پردازش",
                OUT_FOR_DELIVERY: "مرسوله در حال تحویل",
                DELIVERED: "تحویل نهایی",
            };

            if (!(status in mapping)) {
                throw new Error(`Unknown status: ${status}`);
            }

            return mapping[status as keyof typeof mapping];
        };

        // Status mapping for English
        const statusToEn = (status: string) => {
            console.log({ status });
            const mapping = {
                PENDING_SELLER_REVIEW: "Waiting for seller review",
                CONFIRMED_WAITING_PAYMENT: "Confirmed, waiting for payment",
                PAYMENT_CONFIRMED_PROCESSING: "Payment confirmed, processing",
                OUT_FOR_DELIVERY: "Out for delivery",
                DELIVERED: "Delivered",
            };

            if (!(status in mapping)) {
                throw new Error(`Unknown status: ${status}`);
            }

            return mapping[status as keyof typeof mapping];
        };

        // Create notification text based on language
        let text;
        if (isEnglish) {
            text = `Your order status has been updated to "${statusToEn(status)}"`;
        } else {
            text = `وضعیت سبد خرید شما به "${statusToFa(status)}" تغییر پیدا کرد`;
        }

        const channel = await this.getChannelOfUserByUserId(userId);
        if (utils.isNil(channel)) {
            console.log(
                `[notifyOrderStatus] Channel not found for userId: ${userId}`,
            );
            return;
        }

        console.log(
            `[notifyOrderStatus] Sending status update to client: ${clientId}, status: ${status}, language: ${isEnglish ? "English" : "Persian"}`,
        );

        try {
            // Get the client
            const client = await this._clientService.getClientOfUser(
                clientId,
                userId,
            );
            if (utils.isNil(client)) {
                console.log(
                    `[notifyOrderStatus] Client not found: ${clientId}`,
                );
                return;
            }

            // Get the account service for the channel
            const accountService = new instagram.AccountService(
                channel.accessToken,
                false,
            ); // Enable test mode

            // Send the message to the client
            await accountService.sendMessage(client.platformId, text);

            console.log(
                `[notifyOrderStatus] Status update sent successfully to client: ${clientId}`,
            );

            // Add to chat history using runTransaction to get a manager
            await this._repo.runTransaction(async (manager) => {
                await this._chatService.addChatHistory(
                    {
                        channelId: channel.id,
                        clientId: client.id,
                        userId,
                        uniqueKey: utils.generateUuId(),
                        text,
                        type: "ai",
                    },
                    manager,
                );
            });
        } catch (error) {
            console.error(
                `[notifyOrderStatus] Error sending status update:`,
                error,
            );
        }
    };

    processTestMessage = async (args: {
        userId: number;
        clientId: number;
        text: string;
    }) => {
        const { userId, clientId, text } = args;

        console.log(
            `[processTestMessage] Processing test message for userId: ${userId}, clientId: ${clientId}`,
        );

        // Get the channel for the user
        const channel = await this.getChannelOfUserByUserId(userId);
        if (utils.isNil(channel)) {
            throw new errors.NotFoundError("Channel not found for user");
        }

        // Get the client
        const client = await this._clientService.getClientOfUser(
            clientId,
            userId,
        );
        if (utils.isNil(client)) {
            throw new errors.NotFoundError("Client not found");
        }

        // Create a mock Instagram message entry for test mode
        const testEntry: InstagramMessageEntryDto = {
            id: channel.platformId,
            messaging: [
                {
                    sender: { id: client.platformId },
                    recipient: { id: channel.platformId },
                    timestamp: Date.now(),
                    message: {
                        mid: `test_${Date.now()}`,
                        text,
                    },
                },
            ],
        };
        console.log(`[processTestMessage] Test message processed successfully`);

        // Process using the existing Instagram message processing logic
        return await this._processInstagramMessageEntry(testEntry, true);
    };

    // Helper method to determine file type from file path
    private _getFileType = (filePath: string): 'image' | 'video' | 'audio' | 'unknown' => {
        const fileName = filePath.split('?')[0].toLowerCase(); // Remove query parameters
        
        const imageExtensions = ['.png', '.jpg', '.jpeg', '.gif', '.avif', '.webp', '.bmp', '.tiff', '.tif', '.svg', '.ico'];
        const videoExtensions = ['.mp4', '.mpeg', '.mpg', '.mov', '.avi', '.wmv', '.webm', '.ogg', '.ogv', '.3gp', '.flv', '.mkv', '.m4v', '.asf', '.f4v'];
        const audioExtensions = ['.mp3', '.m4a', '.wav', '.ogg', '.oga', '.webm', '.flac', '.aac', '.wma', '.3gp', '.amr', '.opus', '.m4b', '.aiff', '.au'];
        
        if (imageExtensions.some(ext => fileName.endsWith(ext))) {
            return 'image';
        }
        if (videoExtensions.some(ext => fileName.endsWith(ext))) {
            return 'video';
        }
        if (audioExtensions.some(ext => fileName.endsWith(ext))) {
            return 'audio';
        }
        
        return 'unknown';
    };
}
