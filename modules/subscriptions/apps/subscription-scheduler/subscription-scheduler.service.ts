import { inject, injectable, registry } from "tsyringe";
import { RedisConnection } from "../../../common/lib/redis";
import { BullMQModule, createJobProcessor } from "../../../common/lib/bullmq";
import { Job, Processor } from "bullmq";
import UserSubscriptionRepo from "../user-subscriptions/user-subscription.repo";
import { KavenegarService } from "../../../common/lib/sms";
import Logger from "../../../common/lib/metrics/logger";
import { registries } from "./registries/index";

// Redis key prefixes for tracking notifications
const REDIS_KEY_PREFIX = "palette";

// Helper function to create subscription expiry notification keys
const createExpiryNotificationKey = (subscriptionId: number) =>
    `${REDIS_KEY_PREFIX}:subscription:${subscriptionId}:expiry_3day_notif`;

@registry(registries)
@injectable()
export default class SubscriptionSchedulerService {
    private _expiredSubscriptionsQueue: BullMQModule<unknown>;
    private _expiryNotificationQueue: BullMQModule<unknown>;
    private _expiredSubscriptionsJobName: string = "expired-subscriptions-check";
    private _expiryNotificationJobName: string = "expiry-notification-check";
    private _redisConnection: RedisConnection;

    constructor(
        private _userSubscriptionRepo: UserSubscriptionRepo,
        @inject("sms") private _smsService: KavenegarService,
        @inject("queue")
        createQueueModule: <T>(
            queueName: string,
            processor: Processor<T>,
            redisConnection: RedisConnection,
        ) => BullMQModule<T>,
        @inject("redis")
        createRedisConnection: () => RedisConnection,
    ) {
        this._redisConnection = createRedisConnection();

        // Create processor for expired subscriptions check
        const expiredSubscriptionsProcessor: Processor = createJobProcessor(
            this.checkExpiredSubscriptions.bind(this),
        );
        this._expiredSubscriptionsQueue = createQueueModule(
            this._expiredSubscriptionsJobName,
            expiredSubscriptionsProcessor,
            this._redisConnection,
        );

        // Create processor for expiry notifications
        const expiryNotificationProcessor: Processor = createJobProcessor(
            this.checkExpiryNotifications.bind(this),
        );
        this._expiryNotificationQueue = createQueueModule(
            this._expiryNotificationJobName,
            expiryNotificationProcessor,
            this._redisConnection,
        );
    }

    /**
     * Check for expired subscriptions and update their status to EXPIRED
     */
    private async checkExpiredSubscriptions(): Promise<void> {
        try {
            Logger.info("Starting expired subscriptions check...");

            // Find all subscriptions that are EXECUTED but have passed their end date
            const expiredSubscriptions = await this._userSubscriptionRepo.findExpiredSubscriptions();

            if (expiredSubscriptions.length === 0) {
                Logger.info("No expired subscriptions found");
                return;
            }

            Logger.info(`Found ${expiredSubscriptions.length} expired subscriptions`);

            // Update all expired subscriptions to EXPIRED status
            for (const subscription of expiredSubscriptions) {
                await this._userSubscriptionRepo.updateById(subscription.id, {
                    status: "EXPIRED" as any
                });

                Logger.info(`Updated subscription ${subscription.id} to EXPIRED status`, {
                    subscriptionId: subscription.id,
                    userId: subscription.userId,
                    endDate: subscription.endDate
                });
            }

            Logger.info(`Successfully updated ${expiredSubscriptions.length} expired subscriptions`);

        } catch (error) {
            Logger.error("Error checking expired subscriptions:", { error: error instanceof Error ? error.message : String(error) });
        }
    }

    /**
     * Check for subscriptions expiring in 3 days and send SMS notifications
     */
    private async checkExpiryNotifications(): Promise<void> {
        try {
            Logger.info("Starting expiry notifications check...");

            // Find all subscriptions that expire in exactly 3 days
            const subscriptionsNearExpiry = await this._userSubscriptionRepo.findSubscriptionsExpiringInDays(3);

            if (subscriptionsNearExpiry.length === 0) {
                Logger.info("No subscriptions expiring in 3 days found");
                return;
            }

            Logger.info(`Found ${subscriptionsNearExpiry.length} subscriptions expiring in 3 days`);

            const redis = this._redisConnection.getConnection();

            // Send SMS notification for each subscription (only once per subscription)
            for (const subscription of subscriptionsNearExpiry) {
                const notificationKey = createExpiryNotificationKey(subscription.id);

                // Check if we already sent notification for this subscription
                const hasNotified = await redis.get(notificationKey);
                if (hasNotified) {
                    Logger.info(`Notification already sent for subscription ${subscription.id}`);
                    continue;
                }

                try {
                    // Send SMS notification
                    await this.sendExpiryNotificationSMS(subscription);

                    // Mark as notified (set TTL to 7 days to avoid duplicate notifications)
                    const ttlSeconds = 7 * 24 * 60 * 60; // 7 days
                    await redis.setex(notificationKey, ttlSeconds, "1");

                    Logger.info(`Sent expiry notification for subscription ${subscription.id}`, {
                        subscriptionId: subscription.id,
                        userId: subscription.userId,
                        userPhone: subscription.user.phone,
                        endDate: subscription.endDate
                    });

                } catch (error) {
                    Logger.error(`Failed to send expiry notification for subscription ${subscription.id}:`, { error: error instanceof Error ? error.message : String(error) });
                }
            }

            Logger.info(`Completed expiry notifications check`);

        } catch (error) {
            Logger.error("Error checking expiry notifications:", { error: error instanceof Error ? error.message : String(error) });
        }
    }

    /**
     * Send SMS notification to user about subscription expiring in 3 days
     */
    private async sendExpiryNotificationSMS(subscription: any): Promise<void> {
        const { user, plan, endDate } = subscription;
        
        if (!user.phone) {
            Logger.warn(`User ${user.id} has no phone number for expiry notification`);
            return;
        }

        // Format end date
        const endDateFormatted = new Date(endDate).toLocaleDateString('fa-IR');

        // Create SMS message based on app language
        const isEnglish = process.env.PUBLIC_APP_LANG === 'en';
        
        let message: string;
        if (isEnglish) {
            message = `Dear ${user.firstName || 'User'}, your ${plan.name} subscription will expire in 3 days (${endDateFormatted}). Please renew to continue using our services.`;
        } else {
            message = `کاربر گرامی ${user.firstName || ''}, اشتراک ${plan.name} شما در تاریخ ${endDateFormatted} (3 روز دیگر) به پایان می‌رسد. لطفاً برای ادامه استفاده از خدمات، اشتراک خود را تمدید کنید.`;
        }

        // Use a custom SMS template for subscription expiry (you'll need to create this template in Kavenegar)
        try {
            // For now, we'll use a simple SMS send. You can create a custom template in Kavenegar panel
            // and use verifyLookup with the template instead
            await this._smsService.sendSubscriptionExpiryNotification(
                user.phone,
                user.firstName || 'کاربر',
                plan.name,
                endDateFormatted
            );
        } catch (error) {
            Logger.error(`Failed to send SMS to ${user.phone}:`, { error: error instanceof Error ? error.message : String(error) });
            throw error;
        }
    }

    /**
     * Schedule the expired subscriptions check job to run every hour
     */
    async scheduleExpiredSubscriptionsCheck(): Promise<void> {
        try {
            await this._expiredSubscriptionsQueue.addRepeatableJob(
                this._expiredSubscriptionsJobName,
                {},
                { 
                    every: 60 * 60 * 1000 // Every hour
                }
            );
            Logger.info("Scheduled expired subscriptions check job to run every hour");
        } catch (error) {
            Logger.error("Failed to schedule expired subscriptions check job:", { error: error instanceof Error ? error.message : String(error) });
        }
    }

    /**
     * Schedule the expiry notification check job to run every 6 hours
     */
    async scheduleExpiryNotificationCheck(): Promise<void> {
        try {
            await this._expiryNotificationQueue.addRepeatableJob(
                this._expiryNotificationJobName,
                {},
                { 
                    every: 6 * 60 * 60 * 1000 // Every 6 hours
                }
            );
            Logger.info("Scheduled expiry notification check job to run every 6 hours");
        } catch (error) {
            Logger.error("Failed to schedule expiry notification check job:", { error: error instanceof Error ? error.message : String(error) });
        }
    }

    /**
     * Initialize all scheduled jobs
     */
    async initializeScheduledJobs(): Promise<void> {
        await this.scheduleExpiredSubscriptionsCheck();
        await this.scheduleExpiryNotificationCheck();
        Logger.info("Subscription scheduler jobs initialized successfully");
    }

    /**
     * Manually trigger expired subscriptions check (for testing)
     */
    async triggerExpiredSubscriptionsCheck(): Promise<void> {
        await this._expiredSubscriptionsQueue.addJob(this._expiredSubscriptionsJobName, {});
        Logger.info("Manually triggered expired subscriptions check");
    }

    /**
     * Manually trigger expiry notification check (for testing)
     */
    async triggerExpiryNotificationCheck(): Promise<void> {
        await this._expiryNotificationQueue.addJob(this._expiryNotificationJobName, {});
        Logger.info("Manually triggered expiry notification check");
    }

    /**
     * Close all queues
     */
    async close(): Promise<void> {
        await this._expiredSubscriptionsQueue.close();
        await this._expiryNotificationQueue.close();
        Logger.info("Subscription scheduler queues closed");
    }
}
