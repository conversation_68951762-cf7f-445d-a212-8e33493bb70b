import { injectable, inject, registry, delay } from "tsyringe";
import { RedisConnection } from "../../../common/lib/redis";
import UserSubscriptionService from "../user-subscriptions/user-subscription.service";
import SubscriptionPlanService from "../subscriptions-plan/subscription-plan.service";
import InventoryService from "../../../inventories/apps/inventories/inventories.service";
import { registries } from "./registries";

// Feature limit constants
export enum FEATURES {
    CHATBOT_MESSAGE = "CHATBOT_MESSAGE",
    INVENTORY_PRODUCT = "INVENTORY_PRODUCT",
}

// Redis key prefixes for different features
const REDIS_KEY_PREFIX = "palette";

// Helper function to create subscription-based usage keys
const createSubscriptionUsageKey = (subscriptionId: number, feature: FEATURES) => 
    `${REDIS_KEY_PREFIX}:subscription:${subscriptionId}:${feature.toLowerCase()}_usage`;

// Bot message notification keys (still per subscription)
const createNotificationKey = (subscriptionId: number, notificationType: string) =>
    `${REDIS_KEY_PREFIX}:subscription:${subscriptionId}:${notificationType}`;

export interface BotMessageLimitResult {
    isAllowed: boolean;
    shouldNotifyNearLimit: boolean; // Should send 80% warning notification
    shouldNotifyLimitReached: boolean; // Should send limit reached notification
    currentUsage: number;
    limit: number;
    remainingUsage: number;
}

export interface FeatureLimitResult {
    isAllowed: boolean;
    currentUsage: number;
    limit: number;
    remainingUsage: number;
    feature: FEATURES;
}

@registry(registries)
@injectable()
export default class SubscriptionLimitService {
    private _redisConnection: RedisConnection;

    constructor(
        @inject(delay(() => UserSubscriptionService)) private _userSubscriptionService: UserSubscriptionService,
        @inject(delay(() => SubscriptionPlanService)) private _subscriptionPlanService: SubscriptionPlanService,
        @inject(delay(() => InventoryService)) private _inventoryService: InventoryService,
        @inject("redis")
        createRedisConnection: () => RedisConnection,
    ) {
        this._redisConnection = createRedisConnection();
    }

    /**
     * Get active subscription for user (status = EXECUTED, current date between startDate and endDate)
     * @param userId - The user ID
     * @returns Promise<any>
     */
    private async getActiveSubscription(userId: number): Promise<any> {
        const subscription = await this._userSubscriptionService.getCurrentSubscriptionOfUser(userId);
        
        if (!subscription) {
            return null;
        }

        // Check if subscription is actually active (EXECUTED status and within date range)
        const now = new Date();
        const startDate = new Date(subscription.startDate);
        const endDate = new Date(subscription.endDate);
        
        if (subscription.status === 'EXECUTED' && now >= startDate && now <= endDate) {
            return subscription;
        }
        
        return null;
    }

    /**
     * Check if user can send more bot messages and update usage
     * @param userId - The user ID to check
     * @returns Promise<BotMessageLimitResult>
     */
    async checkBotMessageLimit(userId: number): Promise<BotMessageLimitResult> {
        try {
            // Use the existing addFeatureLimit method for CHATBOT_MESSAGE
            const result = await this.addFeatureLimit(userId, FEATURES.CHATBOT_MESSAGE);

            if (!result.isAllowed) {
                // Get active subscription for notification keys
                const subscription = await this.getActiveSubscription(userId);
                
                if (subscription) {
                    // Check if we should notify about limit reached (only once per subscription)
                    const shouldNotifyLimitReached = await this.checkAndSetNotificationFlag(
                        createNotificationKey(subscription.id, "bot_message_limit_reached_notif"),
                        subscription.id
                    );

                    return {
                        isAllowed: false,
                        shouldNotifyNearLimit: false,
                        shouldNotifyLimitReached,
                        currentUsage: result.currentUsage,
                        limit: result.limit,
                        remainingUsage: 0,
                    };
                }

                return {
                    isAllowed: false,
                    shouldNotifyNearLimit: false,
                    shouldNotifyLimitReached: false,
                    currentUsage: result.currentUsage,
                    limit: result.limit,
                    remainingUsage: 0,
                };
            }

            // Check if near limit (80%) and should notify
            const nearLimitThreshold = Math.floor(result.limit * 0.8);
            const isNearLimit = result.currentUsage >= nearLimitThreshold;
            
            let shouldNotifyNearLimit = false;
            
            if (isNearLimit) {
                const subscription = await this.getActiveSubscription(userId);
                if (subscription) {
                    shouldNotifyNearLimit = await this.checkAndSetNotificationFlag(
                        createNotificationKey(subscription.id, "bot_message_near_limit_notif"), 
                        subscription.id
                    );
                }
            }

            return {
                isAllowed: true,
                shouldNotifyNearLimit,
                shouldNotifyLimitReached: false,
                currentUsage: result.currentUsage,
                limit: result.limit,
                remainingUsage: result.remainingUsage,
            };

        } catch (error) {
            console.error('Error checking bot message limit:', error);
            // On error, don't allow to be safe
            return {
                isAllowed: false,
                shouldNotifyNearLimit: false,
                shouldNotifyLimitReached: false,
                currentUsage: 0,
                limit: 0,
                remainingUsage: 0,
            };
        }
    }

    /**
     * Add usage for a specific feature (increment by 1)
     * @param userId - The user ID
     * @param feature - The feature to increment usage for
     * @returns Promise<FeatureLimitResult>
     */
    async addFeatureLimit(userId: number, feature: FEATURES): Promise<FeatureLimitResult> {
        try {
            // Get active subscription
            const subscription = await this.getActiveSubscription(userId);

            if (!subscription) {
                return {
                    isAllowed: false,
                    currentUsage: 0,
                    limit: 0,
                    remainingUsage: 0,
                    feature,
                };
            }

            // Get full subscription plan details
            const subscriptionPlan = await this._subscriptionPlanService.getSubscriptionPlan(subscription.plan.id);

            if (!subscriptionPlan) {
                return {
                    isAllowed: false,
                    currentUsage: 0,
                    limit: 0,
                    remainingUsage: 0,
                    feature,
                };
            }

            const featureLimit = this.getFeatureLimitFromPlan(subscriptionPlan, feature);

            // If no limit is set (unlimited), allow
            if (!featureLimit || featureLimit <= 0) {
                return {
                    isAllowed: true,
                    currentUsage: 0,
                    limit: 0,
                    remainingUsage: 0,
                    feature,
                };
            }

            // Generate Redis key for this subscription and feature
            const usageKey = createSubscriptionUsageKey(subscription.id, feature);

            // Calculate TTL to subscription end date (in seconds)
            const endDate = new Date(subscription.endDate);
            const now = new Date();
            const ttlSeconds = Math.floor((endDate.getTime() - now.getTime()) / 1000);

            // If TTL is negative or zero, subscription has expired
            if (ttlSeconds <= 0) {
                return {
                    isAllowed: false,
                    currentUsage: 0,
                    limit: featureLimit,
                    remainingUsage: 0,
                    feature,
                };
            }

            // Get current usage
            const redis = this._redisConnection.getConnection();
            const currentUsageStr = await redis.get(usageKey);
            const currentUsage = currentUsageStr ? parseInt(currentUsageStr, 10) : 0;

            // Check if limit is reached
            if (currentUsage >= featureLimit) {
                return {
                    isAllowed: false,
                    currentUsage,
                    limit: featureLimit,
                    remainingUsage: 0,
                    feature,
                };
            }

            // Increment usage
            const newUsage = currentUsage + 1;

            // Set usage with TTL to subscription end date
            await redis.setex(usageKey, ttlSeconds, newUsage.toString());

            return {
                isAllowed: true,
                currentUsage: newUsage,
                limit: featureLimit,
                remainingUsage: featureLimit - newUsage,
                feature,
            };

        } catch (error) {
            console.error(`Error adding feature limit for ${feature}:`, error);
            // On error, don't allow to be safe
            return {
                isAllowed: false,
                currentUsage: 0,
                limit: 0,
                remainingUsage: 0,
                feature,
            };
        }
    }

    /**
     * Remove usage for a specific feature (decrement by 1)
     * @param userId - The user ID
     * @param feature - The feature to decrement usage for
     * @returns Promise<FeatureLimitResult>
     */
    async removeFeatureLimit(userId: number, feature: FEATURES): Promise<FeatureLimitResult> {
        try {
            // Get active subscription
            const subscription = await this.getActiveSubscription(userId);

            if (!subscription) {
                return {
                    isAllowed: true,
                    currentUsage: 0,
                    limit: 0,
                    remainingUsage: 0,
                    feature,
                };
            }

            // Get full subscription plan details
            const subscriptionPlan = await this._subscriptionPlanService.getSubscriptionPlan(subscription.plan.id);

            if (!subscriptionPlan) {
                return {
                    isAllowed: true,
                    currentUsage: 0,
                    limit: 0,
                    remainingUsage: 0,
                    feature,
                };
            }

            const featureLimit = this.getFeatureLimitFromPlan(subscriptionPlan, feature);

            // Generate Redis key for this subscription and feature
            const usageKey = createSubscriptionUsageKey(subscription.id, feature);

            // Calculate TTL to subscription end date (in seconds)
            const endDate = new Date(subscription.endDate);
            const now = new Date();
            const ttlSeconds = Math.floor((endDate.getTime() - now.getTime()) / 1000);

            // Get current usage
            const redis = this._redisConnection.getConnection();
            const currentUsageStr = await redis.get(usageKey);
            const currentUsage = currentUsageStr ? parseInt(currentUsageStr, 10) : 0;

            // Decrement usage (don't go below 0)
            const newUsage = Math.max(0, currentUsage - 1);

            // Set usage with TTL to subscription end date
            if (newUsage > 0 && ttlSeconds > 0) {
                await redis.setex(usageKey, ttlSeconds, newUsage.toString());
            } else {
                await redis.del(usageKey);
            }

            return {
                isAllowed: true,
                currentUsage: newUsage,
                limit: featureLimit || 0,
                remainingUsage: featureLimit ? featureLimit - newUsage : 0,
                feature,
            };

        } catch (error) {
            console.error(`Error removing feature limit for ${feature}:`, error);
            return {
                isAllowed: true,
                currentUsage: 0,
                limit: 0,
                remainingUsage: 0,
                feature,
            };
        }
    }

    /**
     * Check current usage for a specific feature without incrementing
     * @param userId - The user ID
     * @param feature - The feature to check usage for
     * @returns Promise<FeatureLimitResult>
     */
    async checkFeatureLimit(userId: number, feature: FEATURES): Promise<FeatureLimitResult> {
        try {
            // Get active subscription
            const subscription = await this.getActiveSubscription(userId);

            if (!subscription) {
                return {
                    isAllowed: false,
                    currentUsage: 0,
                    limit: 0,
                    remainingUsage: 0,
                    feature,
                };
            }

            // Get full subscription plan details
            const subscriptionPlan = await this._subscriptionPlanService.getSubscriptionPlan(subscription.plan.id);

            if (!subscriptionPlan) {
                return {
                    isAllowed: false,
                    currentUsage: 0,
                    limit: 0,
                    remainingUsage: 0,
                    feature,
                };
            }

            const featureLimit = this.getFeatureLimitFromPlan(subscriptionPlan, feature);

            // If no limit is set (unlimited), allow
            if (!featureLimit || featureLimit <= 0) {
                return {
                    isAllowed: true,
                    currentUsage: 0,
                    limit: 0,
                    remainingUsage: 0,
                    feature,
                };
            }

            // Generate Redis key for this subscription and feature
            const usageKey = createSubscriptionUsageKey(subscription.id, feature);

            // Get current usage
            const redis = this._redisConnection.getConnection();
            const currentUsageStr = await redis.get(usageKey);
            const currentUsage = currentUsageStr ? parseInt(currentUsageStr, 10) : 0;

            // Check if limit is reached
            const isAllowed = currentUsage < featureLimit;

            return {
                isAllowed,
                currentUsage,
                limit: featureLimit,
                remainingUsage: Math.max(0, featureLimit - currentUsage),
                feature,
            };

        } catch (error) {
            console.error(`Error checking feature limit for ${feature}:`, error);
            // On error, don't allow to be safe
            return {
                isAllowed: false,
                currentUsage: 0,
                limit: 0,
                remainingUsage: 0,
                feature,
            };
        }
    }

    /**
     * Check if user is allowed to add more products (inventory limit check)
     * @param userId - The user ID to check
     * @returns Promise<FeatureLimitResult>
     */
    async checkInventoryLimit(userId: number): Promise<FeatureLimitResult> {
        try {
            // Get active subscription
            const subscription = await this.getActiveSubscription(userId);

            if (!subscription) {
                return {
                    isAllowed: false,
                    currentUsage: 0,
                    limit: 0,
                    remainingUsage: 0,
                    feature: FEATURES.INVENTORY_PRODUCT,
                };
            }

            // Get full subscription plan details
            const subscriptionPlan = await this._subscriptionPlanService.getSubscriptionPlan(subscription.plan.id);

            if (!subscriptionPlan) {
                return {
                    isAllowed: false,
                    currentUsage: 0,
                    limit: 0,
                    remainingUsage: 0,
                    feature: FEATURES.INVENTORY_PRODUCT,
                };
            }

            const inventoryLimit = this.getFeatureLimitFromPlan(subscriptionPlan, FEATURES.INVENTORY_PRODUCT);

            // If no limit is set (unlimited), allow
            if (!inventoryLimit || inventoryLimit <= 0) {
                return {
                    isAllowed: true,
                    currentUsage: 0,
                    limit: 0,
                    remainingUsage: 0,
                    feature: FEATURES.INVENTORY_PRODUCT,
                };
            }

            // Get current inventory count from InventoryService (not Redis based)
            const currentInventoryCount = await this._inventoryService.getInventoriesCountOfUser(userId);

            // Check if limit is reached
            const isAllowed = currentInventoryCount < inventoryLimit;

            return {
                isAllowed,
                currentUsage: currentInventoryCount,
                limit: inventoryLimit,
                remainingUsage: Math.max(0, inventoryLimit - currentInventoryCount),
                feature: FEATURES.INVENTORY_PRODUCT,
            };

        } catch (error) {
            console.error('Error checking inventory limit:', error);
            // On error, don't allow to be safe
            return {
                isAllowed: false,
                currentUsage: 0,
                limit: 0,
                remainingUsage: 0,
                feature: FEATURES.INVENTORY_PRODUCT,
            };
        }
    }

    /**
     * Helper method to get feature limit from subscription plan
     * @param subscriptionPlan - The subscription plan
     * @param feature - The feature to get limit for
     * @returns The limit for the feature
     */
    private getFeatureLimitFromPlan(subscriptionPlan: any, feature: FEATURES): number {
        switch (feature) {
            case FEATURES.CHATBOT_MESSAGE:
                return subscriptionPlan.botLimit;
            case FEATURES.INVENTORY_PRODUCT:
                return subscriptionPlan.inventoryLimit;
            default:
                return 0;
        }
    }

    /**
     * Helper method to check and set notification flag for a subscription
     * @param notificationKey - The notification Redis key
     * @param subscriptionId - The subscription ID
     * @returns Whether notification should be sent
     */
    private async checkAndSetNotificationFlag(notificationKey: string, subscriptionId: number): Promise<boolean> {
        const redis = this._redisConnection.getConnection();

        const hasNotified = await redis.get(notificationKey);
        const shouldNotify = !hasNotified;

        if (shouldNotify) {
            // For notifications, we need to get the subscription details to set proper TTL
            // Since we have subscription ID, we can find it by querying the repo directly
            // But for simplicity, let's just set a long TTL (30 days) since notifications 
            // are per-subscription anyway
            const ttlSeconds = 30 * 24 * 60 * 60; // 30 days
            await redis.setex(notificationKey, ttlSeconds, "1");
        }

        return shouldNotify;
    }

}